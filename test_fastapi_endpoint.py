#!/usr/bin/env python3
"""
Test script to verify FastAPI LinkedIn extraction endpoint works
"""

import asyncio
import json
import requests
import time
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
import sys
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_fastapi_endpoint():
    """Test the FastAPI endpoint directly."""
    
    print("🚀 Testing FastAPI LinkedIn Extraction Endpoint")
    print("=" * 50)
    
    # Test data
    test_data = {
        "url": "https://www.linkedin.com/in/yashkhivasara/",
        "email": "<EMAIL>",
        "priority": 1
    }
    
    # Test the endpoint
    try:
        print("📡 Sending request to FastAPI endpoint...")
        print(f"URL: http://localhost:8000/ingest/linkedin-minimal")
        print(f"Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            "http://localhost:8000/ingest/linkedin-minimal",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response:")
            print(json.dumps(result, indent=2))
            
            # The response should contain volunteer_id and status
            if "volunteer_id" in result and "status" in result:
                print(f"\n🎯 Volunteer ID: {result['volunteer_id']}")
                print(f"🎯 Status: {result['status']}")
                print(f"🎯 Message: {result.get('message', 'N/A')}")
                
                if result['status'] == 'processing':
                    print("\n✅ LinkedIn profile submitted for background processing!")
                    print("💡 The extraction will happen in the background.")
                    print("💡 In a real application, you would check the status later.")
                else:
                    print(f"\n⚠️ Unexpected status: {result['status']}")
            else:
                print("\n❌ Response missing expected fields (volunteer_id, status)")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the FastAPI server running on port 8000?")
        print("💡 Start the server with: python -m uvicorn services.api.main:app --host 0.0.0.0 --port 8000")
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 30 seconds")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

async def test_direct_import():
    """Test importing and calling the FastAPI app directly."""
    
    print("\n" + "=" * 50)
    print("🔧 Testing Direct FastAPI Import")
    print("=" * 50)
    
    try:
        from services.api.main import app
        from fastapi.testclient import TestClient
        
        print("✅ Successfully imported FastAPI app")
        
        # Create test client
        client = TestClient(app)
        
        # Test data
        test_data = {
            "url": "https://www.linkedin.com/in/yashkhivasara/",
            "email": "<EMAIL>",
            "priority": 1
        }
        
        print("📡 Testing endpoint with TestClient...")
        response = client.post("/ingest/linkedin-minimal", json=test_data)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Response:")
            print(json.dumps(result, indent=2))
            
            if "volunteer_id" in result and "status" in result:
                print(f"\n🎯 Volunteer ID: {result['volunteer_id']}")
                print(f"🎯 Status: {result['status']}")
                print(f"🎯 Message: {result.get('message', 'N/A')}")
                
                if result['status'] == 'processing':
                    print("\n✅ LinkedIn profile submitted for background processing!")
                    print("💡 The extraction will happen in the background.")
                else:
                    print(f"\n⚠️ Unexpected status: {result['status']}")
            else:
                print("\n❌ Response missing expected fields (volunteer_id, status)")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing direct import: {e}")

async def main():
    """Main test function."""
    await test_fastapi_endpoint()
    await test_direct_import()

if __name__ == "__main__":
    asyncio.run(main())
