# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database & ORM
sqlmodel==0.0.24
alembic==1.16.2
psycopg2-binary==2.9.10
asyncpg==0.29.0
supabase==2.8.0
sqlalchemy[asyncio]==2.0.23

# Pydantic & Settings (Exact working versions)
pydantic==2.11.7
pydantic-settings==2.10.1
email-validator==2.2.0

# Vector Database
qdrant-client==1.14.3

# NLP & ML (Exact working versions)
spacy==3.8.7
sentence-transformers==5.0.0
transformers==4.53.0
torch==2.7.1
numpy==2.3.1

# Browser Automation & Web Scraping
browser-use==0.1.40
playwright==1.53.0
aiohttp==3.12.13
requests==2.32.4

# PDF Processing
pdfplumber==0.11.7
pytesseract==0.3.13
Pillow==11.3.0
python-docx==1.2.0
PyPDF2==3.0.1
openpyxl==3.1.5

# AWS & Cloud
boto3==1.39.2
botocore==1.39.2

# Security & Auth
PyJWT[crypto]==2.10.1
cryptography==45.0.5
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.5.0

# Utilities
tenacity==8.5.0
python-dotenv==1.1.1
python-multipart==0.0.20
httpx==0.27.2

# Monitoring & Observability
prometheus-client==0.22.1
structlog==25.4.0

# Data Processing
pandas==2.3.0
python-dateutil==2.9.0.post0

# OpenAI (supports Azure OpenAI)
openai==1.93.0

# Additional Utilities
click==8.2.1

# Development Dependencies (optional)
pytest==8.4.1
pytest-asyncio==1.0.0
pytest-cov==6.2.1
pytest-mock==3.14.1
black==25.1.0
isort==6.0.1
mypy==1.16.1
flake8==7.3.0
pre-commit==4.2.0
types-requests==2.32.4.20250611
types-python-dateutil==2.9.0.20250516
types-Pillow==10.2.0.20240822 