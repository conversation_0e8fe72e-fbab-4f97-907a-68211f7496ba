"""
Query endpoints for the skill extractor API.

This module provides endpoints for retrieving volunteer profiles and performing
vector similarity searches against the Qdrant database.
"""

import time
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, Query, status
from fastapi.responses import JSONResponse

from common.models import VectorSearchRequest, VectorSearchResponse, VectorSearchResult
from common.settings import settings
from common.logging import get_logger
from services.api.middleware import get_current_user, require_role
from services.api.clients.qdrant import get_qdrant_client, QdrantClient
from services.api.clients.database import get_database_client, DatabaseClient

logger = get_logger(__name__)

router = APIRouter()


@router.get("/volunteer/{volunteer_id}")
async def get_volunteer_profile(
    volunteer_id: UUID,
    include_vector: bool = Query(False, description="Include vector data in response"),
    current_user: dict = Depends(get_current_user),
    db_client: DatabaseClient = Depends(get_database_client),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Retrieve a complete volunteer profile by ID.
    
    This endpoint returns merged data from PostgreSQL (structured profile data)
    and optionally from Qdrant (vector embeddings and metadata).
    """
    
    logger.info(
        "Volunteer profile requested",
        volunteer_id=str(volunteer_id),
        include_vector=include_vector,
        user_id=current_user.get("sub")
    )
    
    try:
        # Get structured data from PostgreSQL
        volunteer_data = await db_client.get_volunteer_by_id(volunteer_id)
        
        if not volunteer_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Volunteer not found",
                    "message": f"No volunteer found with ID {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )
        
        response_data = {
            "volunteer_id": volunteer_id,
            "profile": volunteer_data,
            "last_updated": volunteer_data.get("updated_at"),
            "source_types": volunteer_data.get("source_types", [])
        }
        
        # Optionally include vector data
        if include_vector:
            try:
                vector_data = await qdrant_client.get_point(str(volunteer_id))
                if vector_data:
                    response_data["vector"] = {
                        "embedding": vector_data.get("vector"),
                        "metadata": vector_data.get("payload", {}),
                        "score": vector_data.get("score")
                    }
                else:
                    response_data["vector"] = None
                    
            except Exception as e:
                logger.warning(
                    "Failed to retrieve vector data",
                    volunteer_id=str(volunteer_id),
                    error=str(e)
                )
                response_data["vector"] = None
                response_data["vector_error"] = "Vector data unavailable"
        
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve volunteer profile",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Profile retrieval failed",
                "message": "Failed to retrieve volunteer profile",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.get("/vector/{volunteer_id}")
async def get_volunteer_vector(
    volunteer_id: UUID,
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Retrieve vector representation for a specific volunteer.
    
    This endpoint returns the vector embedding and associated metadata
    from the Qdrant vector database.
    """
    
    logger.info(
        "Vector data requested",
        volunteer_id=str(volunteer_id),
        user_id=current_user.get("sub")
    )
    
    try:
        vector_data = await qdrant_client.get_point(str(volunteer_id))
        
        if not vector_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "Vector not found",
                    "message": f"No vector found for volunteer {volunteer_id}",
                    "volunteer_id": str(volunteer_id)
                }
            )
        
        response = {
            "volunteer_id": volunteer_id,
            "vector": vector_data.get("vector"),
            "metadata": vector_data.get("payload", {}),
            "vector_size": len(vector_data.get("vector", [])),
            "last_updated": vector_data.get("payload", {}).get("last_updated")
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve vector data",
            volunteer_id=str(volunteer_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Vector retrieval failed",
                "message": "Failed to retrieve vector data",
                "volunteer_id": str(volunteer_id)
            }
        )


@router.post("/match", response_model=VectorSearchResponse)
async def vector_similarity_search(
    request: VectorSearchRequest,
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Perform vector similarity search to find matching volunteers.
    
    This endpoint supports multiple search modes:
    1. Vector-based search: Provide a query vector directly
    2. Text-based search: Provide text that will be vectorized  
    3. Volunteer-based search: Use another volunteer's vector as the query
    """
    
    start_time = time.time()
    
    logger.info(
        "Vector similarity search requested",
        has_query_vector=request.query_vector is not None,
        has_query_text=request.query_text is not None,
        reference_volunteer_id=str(request.volunteer_id) if request.volunteer_id else None,
        top_k=request.top_k,
        user_id=current_user.get("sub")
    )
    
    try:
        # Determine query vector
        query_vector = None
        
        if request.query_vector:
            query_vector = request.query_vector
        elif request.volunteer_id:
            # Use another volunteer's vector as the query
            reference_vector_data = await qdrant_client.get_point(str(request.volunteer_id))
            if not reference_vector_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "error": "Reference volunteer not found",
                        "message": f"No vector found for volunteer {request.volunteer_id}"
                    }
                )
            query_vector = reference_vector_data.get("vector")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "Invalid search request",
                    "message": "Must provide query_vector or volunteer_id"
                }
            )
        
        # Perform vector search
        search_results = await qdrant_client.search_similar(
            query_vector=query_vector,
            top_k=request.top_k,
            score_threshold=request.score_threshold,
            filters=request.filters
        )
        
        # Convert results
        enriched_results = []
        for result in search_results:
            enriched_result = VectorSearchResult(
                volunteer_id=UUID(result["id"]),
                score=result["score"],
                metadata=result.get("payload", {})
            )
            enriched_results.append(enriched_result)
        
        query_time_ms = (time.time() - start_time) * 1000
        
        response = VectorSearchResponse(
            results=enriched_results,
            total_results=len(enriched_results),
            query_time_ms=query_time_ms
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Vector similarity search failed",
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Search failed",
                "message": "Failed to perform vector similarity search"
            }
        )


@router.get("/search/similar/{volunteer_id}")
async def find_similar_volunteers(
    volunteer_id: UUID,
    top_k: int = Query(10, ge=1, le=100, description="Number of similar volunteers to return"),
    score_threshold: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum similarity score"),
    current_user: dict = Depends(get_current_user),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Find volunteers similar to a given volunteer.
    
    This is a convenience endpoint that uses an existing volunteer's vector
    to find other similar volunteers in the database.
    """
    
    logger.info(
        "Similar volunteers search requested",
        reference_volunteer_id=str(volunteer_id),
        top_k=top_k,
        score_threshold=score_threshold,
        user_id=current_user.get("sub")
    )
    
    # Create a search request and delegate to the main search endpoint
    search_request = VectorSearchRequest(
        volunteer_id=volunteer_id,
        top_k=top_k,
        score_threshold=score_threshold
    )
    
    return await vector_similarity_search(
        request=search_request,
        current_user=current_user,
        qdrant_client=qdrant_client
    )


@router.get("/stats")
async def get_database_statistics(
    current_user: dict = Depends(require_role("admin")),
    db_client: DatabaseClient = Depends(get_database_client),
    qdrant_client: QdrantClient = Depends(get_qdrant_client)
):
    """
    Get database statistics and metrics.
    
    This endpoint provides information about the current state of both
    the PostgreSQL database and Qdrant vector database.
    
    Requires 'admin' role for authentication.
    """
    
    logger.info(
        "Database statistics requested",
        user_id=current_user.get("sub")
    )
    
    try:
        # Get Qdrant statistics
        qdrant_stats = await qdrant_client.get_collection_info()
        
        return {
            "timestamp": time.time(),
            "qdrant": qdrant_stats,
            "system_health": "healthy"
        }
        
    except Exception as e:
        logger.error(
            "Failed to get database statistics",
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Statistics unavailable",
                "message": "Failed to retrieve database statistics"
            }
        ) 