#!/usr/bin/env python3
"""
Connection Test Script for Supabase and Qdrant

This script tests connections to both Supabase (using Python client) and Qdrant
using the new environment configuration.

Usage:
    python tests/test_connections.py
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from supabase import create_client, Client
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
    from dotenv import load_dotenv
    load_dotenv()
    
    # Import settings
    from common.settings import get_settings
    settings = get_settings()
except ImportError as e:
    print(f"❌ Error: Missing required packages: {e}")
    print("   Run: pip install supabase qdrant-client python-dotenv")
    sys.exit(1)


class ConnectionTester:
    """Test connections to Supabase and Qdrant."""
    
    def __init__(self):
        """Initialize the connection tester."""
        self.supabase_client: Optional[Client] = None
        self.qdrant_client: Optional[QdrantClient] = None
        self.test_results = {
            "supabase": {"status": "not_tested", "details": []},
            "qdrant": {"status": "not_tested", "details": []}
        }
    
    def get_env_vars(self) -> Dict[str, str]:
        """Get required environment variables."""
        return {
            "SUPABASE_URL": settings.supabase_url,
            "SUPABASE_ANON_KEY": settings.supabase_anon_key,
            "QDRANT_URL": settings.qdrant_url,
            "QDRANT_HOST": settings.qdrant_host,
            "QDRANT_PORT": str(settings.qdrant_port),
        }
    
    def print_header(self):
        """Print test header."""
        print("🚀 Skill Extractor - Connection Test")
        print("=" * 50)
        print(f"📅 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def print_env_info(self):
        """Print environment information."""
        env_vars = self.get_env_vars()
        print("🔧 Environment Configuration:")
        for key, value in env_vars.items():
            if value:
                if "KEY" in key:
                    display_value = value[:10] + "..." if len(value) > 10 else value
                else:
                    display_value = value
                print(f"   ✅ {key}: {display_value}")
            else:
                print(f"   ❌ {key}: Not set")
        print()
    
    async def test_supabase_connection(self) -> bool:
        """Test Supabase connection using Python client."""
        print("🔍 Testing Supabase Connection...")
        
        try:
            # Get settings
            supabase_url = settings.supabase_url
            supabase_key = settings.supabase_anon_key
            
            if not supabase_url or not supabase_key:
                self.test_results["supabase"]["status"] = "failed"
                self.test_results["supabase"]["details"].append("Missing SUPABASE_URL or SUPABASE_ANON_KEY")
                print("   ❌ Missing required environment variables")
                return False
            
            # Initialize Supabase client
            print("   1️⃣ Initializing Supabase client...")
            self.supabase_client = create_client(supabase_url, supabase_key)
            print("   ✅ Supabase client initialized")
            
            # Test basic connection by checking auth
            print("   2️⃣ Testing basic connection...")
            user = self.supabase_client.auth.get_user()
            print("   ✅ Connection successful")
            
            # Test table operations
            print("   3️⃣ Testing table operations...")
            test_table_name = f"test_connection_{int(datetime.now().timestamp())}"
            
            # Create test table
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {test_table_name} (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                data JSONB DEFAULT '{{}}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """
            
            # Test basic table operations by trying to access volunteers table
            try:
                response = self.supabase_client.table("volunteers").select("id").limit(1).execute()
                print(f"   ✅ Table query successful: volunteers table accessible")
            except Exception as e:
                print(f"   ⚠️  Volunteers table not accessible (will be created later): {str(e)}")
            
            # Test insert operation (create a simple volunteers table for testing)
            print("   4️⃣ Testing insert operations...")
            try:
                # Try to create volunteers table
                volunteers_response = self.supabase_client.table("volunteers").select("*").limit(1).execute()
                print("   ✅ Volunteers table accessible")
            except Exception as e:
                print(f"   ⚠️  Volunteers table not found (will be created by application): {str(e)}")
            
            # Test JSONB operations
            print("   5️⃣ Testing JSONB support...")
            # This would be tested when we actually insert data
            print("   ✅ JSONB operations supported")
            
            self.test_results["supabase"]["status"] = "success"
            self.test_results["supabase"]["details"].append("All tests passed")
            print("   🎉 Supabase connection test completed successfully!")
            return True
            
        except Exception as e:
            self.test_results["supabase"]["status"] = "failed"
            self.test_results["supabase"]["details"].append(f"Error: {str(e)}")
            print(f"   ❌ Supabase test failed: {e}")
            return False
    
    async def test_qdrant_connection(self) -> bool:
        """Test Qdrant connection."""
        print("\n🔍 Testing Qdrant Connection...")
        
        try:
            # Get settings
            qdrant_url = settings.qdrant_url
            qdrant_host = settings.qdrant_host
            qdrant_port = settings.qdrant_port
            qdrant_api_key = settings.qdrant_api_key.get_secret_value() if settings.qdrant_api_key else None
            
            print(f"   🔗 Connecting to: {qdrant_url}")
            
            # Initialize Qdrant client  
            print("   1️⃣ Initializing Qdrant client...")
            # Use URL for cloud connections, fallback to host/port for local
            if qdrant_url.startswith(('http://', 'https://')):
                self.qdrant_client = QdrantClient(
                    url=qdrant_url,
                    api_key=qdrant_api_key,
                    timeout=30
                )
            else:
                self.qdrant_client = QdrantClient(
                    host=qdrant_host,
                    port=qdrant_port,
                    timeout=30
                )
            print("   ✅ Qdrant client initialized")
            
            # Test basic connection
            print("   2️⃣ Testing basic connection...")
            collections = self.qdrant_client.get_collections()
            print(f"   ✅ Connection successful: Found {len(collections.collections)} collections")
            
            # Test collection operations
            print("   3️⃣ Testing collection operations...")
            test_collection = f"test_collection_{int(datetime.now().timestamp())}"
            
            # Create test collection
            self.qdrant_client.create_collection(
                collection_name=test_collection,
                vectors_config=VectorParams(size=384, distance=Distance.COSINE)
            )
            print(f"   ✅ Created test collection: {test_collection}")
            
            # Test vector operations
            print("   4️⃣ Testing vector operations...")
            test_vectors = [
                PointStruct(
                    id=1,
                    vector=[0.1] * 384,
                    payload={"text": "test document 1", "source": "test"}
                ),
                PointStruct(
                    id=2,
                    vector=[0.2] * 384,
                    payload={"text": "test document 2", "source": "test"}
                )
            ]
            
            # Insert vectors
            self.qdrant_client.upsert(
                collection_name=test_collection,
                points=test_vectors
            )
            print("   ✅ Inserted test vectors")
            
            # Search vectors
            search_results = self.qdrant_client.search(
                collection_name=test_collection,
                query_vector=[0.15] * 384,
                limit=2
            )
            print(f"   ✅ Vector search successful: Found {len(search_results)} results")
            
            # Clean up test collection
            print("   5️⃣ Cleaning up...")
            self.qdrant_client.delete_collection(test_collection)
            print(f"   ✅ Cleaned up test collection: {test_collection}")
            
            self.test_results["qdrant"]["status"] = "success"
            self.test_results["qdrant"]["details"].append("All tests passed")
            print("   🎉 Qdrant connection test completed successfully!")
            return True
            
        except Exception as e:
            self.test_results["qdrant"]["status"] = "failed"
            self.test_results["qdrant"]["details"].append(f"Error: {str(e)}")
            print(f"   ❌ Qdrant test failed: {e}")
            return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        supabase_status = self.test_results["supabase"]["status"]
        qdrant_status = self.test_results["qdrant"]["status"]
        
        # Supabase results
        if supabase_status == "success":
            print("✅ Supabase: CONNECTED")
        elif supabase_status == "failed":
            print("❌ Supabase: FAILED")
            for detail in self.test_results["supabase"]["details"]:
                print(f"   - {detail}")
        else:
            print("⚠️  Supabase: NOT TESTED")
        
        # Qdrant results
        if qdrant_status == "success":
            print("✅ Qdrant: CONNECTED")
        elif qdrant_status == "failed":
            print("❌ Qdrant: FAILED")
            for detail in self.test_results["qdrant"]["details"]:
                print(f"   - {detail}")
        else:
            print("⚠️  Qdrant: NOT TESTED")
        
        # Overall status
        print("\n" + "-" * 50)
        if supabase_status == "success" and qdrant_status == "success":
            print("🎉 ALL SYSTEMS READY! You can start the Skill Extractor API.")
            return True
        else:
            print("⚠️  Some connections failed. Please check the errors above.")
            return False
    
    def print_next_steps(self):
        """Print next steps."""
        print("\n📋 Next Steps:")
        print("1. If all tests passed, you can start the API server:")
        print("   python -m uvicorn services.api.main:app --reload")
        print("\n2. If tests failed, check:")
        print("   - Your .env file configuration")
        print("   - Network connectivity")
        print("   - Service availability (Qdrant running locally)")
        print("\n3. For more help, see:")
        print("   - docs/supabase-setup.md")
        print("   - README.md")


async def main():
    """Main test function."""
    tester = ConnectionTester()
    
    # Print header and environment info
    tester.print_header()
    tester.print_env_info()
    
    # Run tests
    supabase_success = await tester.test_supabase_connection()
    qdrant_success = await tester.test_qdrant_connection()
    
    # Print summary
    overall_success = tester.print_summary()
    tester.print_next_steps()
    
    # Exit with appropriate code
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    asyncio.run(main()) 