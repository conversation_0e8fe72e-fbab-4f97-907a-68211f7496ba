"""
Structured logging configuration for the skill extractor backend.

This module provides JSON-formatted logging with request ID tracing,
performance metrics, and proper log levels for different environments.
"""

import json
import logging
import sys
import time
import uuid
from contextvars import ContextVar
from typing import Any, Dict, Optional

import structlog
from structlog.types import FilteringBoundLogger


# Context variable for request ID tracking
request_id_var: ContextVar[Optional[str]] = ContextVar("request_id", default=None)


def get_request_id() -> Optional[str]:
    """Get the current request ID from context."""
    return request_id_var.get()


def set_request_id(request_id: str) -> None:
    """Set the request ID in context."""
    request_id_var.set(request_id)


def generate_request_id() -> str:
    """Generate a new request ID."""
    return str(uuid.uuid4())


class RequestIDProcessor:
    """Structlog processor to add request ID to log records."""
    
    def __call__(self, logger: FilteringBoundLogger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Add request ID to the event dictionary."""
        request_id = get_request_id()
        if request_id:
            event_dict["request_id"] = request_id
        return event_dict


class TimestampProcessor:
    """Structlog processor to add ISO timestamp."""
    
    def __call__(self, logger: FilteringBoundLogger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Add ISO timestamp to the event dictionary."""
        from datetime import datetime, timezone
        event_dict["timestamp"] = time.time()
        event_dict["iso_timestamp"] = datetime.now(timezone.utc).isoformat()
        return event_dict


class ServiceProcessor:
    """Structlog processor to add service information."""
    
    def __init__(self, service_name: str, version: str = "1.0.0"):
        self.service_name = service_name
        self.version = version
    
    def __call__(self, logger: FilteringBoundLogger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Add service information to the event dictionary."""
        event_dict["service"] = self.service_name
        event_dict["version"] = self.version
        return event_dict


def configure_logging(
    service_name: str,
    log_level: str = "INFO",
    json_logs: bool = True,
    version: str = "1.0.0"
) -> None:
    """
    Configure structured logging for the application.
    
    Args:
        service_name: Name of the service (api, worker_extraction, worker_vector)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        json_logs: Whether to output JSON formatted logs
        version: Service version
    """
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Configure structlog processors
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        RequestIDProcessor(),
        TimestampProcessor(),
        ServiceProcessor(service_name, version),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if json_logs:
        # JSON output for production
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable output for development
        processors.append(structlog.dev.ConsoleRenderer())
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=numeric_level,
    )
    
    # Set log levels for noisy libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


def get_logger(name: str = None) -> FilteringBoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggingMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> FilteringBoundLogger:
        """Get a logger instance for this class."""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs) -> None:
    """
    Log a function call with parameters.
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger("function_call")
    logger.info(
        "Function called",
        function=func_name,
        parameters=kwargs
    )


def log_performance(operation: str, duration_ms: float, **metadata) -> None:
    """
    Log performance metrics for an operation.
    
    Args:
        operation: Name of the operation
        duration_ms: Duration in milliseconds
        **metadata: Additional metadata about the operation
    """
    logger = get_logger("performance")
    logger.info(
        "Operation completed",
        operation=operation,
        duration_ms=duration_ms,
        **metadata
    )


def log_error(error: Exception, context: str, **metadata) -> None:
    """
    Log an error with context and metadata.
    
    Args:
        error: The exception that occurred
        context: Context where the error occurred
        **metadata: Additional metadata about the error
    """
    logger = get_logger("error")
    logger.error(
        "Error occurred",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context,
        **metadata,
        exc_info=True
    )


class PerformanceTimer:
    """Context manager for measuring and logging operation performance."""
    
    def __init__(self, operation: str, logger: Optional[FilteringBoundLogger] = None, **metadata):
        self.operation = operation
        self.logger = logger or get_logger("performance")
        self.metadata = metadata
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.debug(f"Starting {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration_ms = (time.time() - self.start_time) * 1000
            
            if exc_type is not None:
                self.logger.error(
                    f"Operation {self.operation} failed",
                    duration_ms=duration_ms,
                    error_type=exc_type.__name__ if exc_type else None,
                    error_message=str(exc_val) if exc_val else None,
                    **self.metadata
                )
            else:
                self.logger.info(
                    f"Operation {self.operation} completed",
                    duration_ms=duration_ms,
                    **self.metadata
                )


# Convenience function for timing operations
def timed_operation(operation: str, **metadata):
    """
    Decorator for timing function execution.
    
    Args:
        operation: Name of the operation
        **metadata: Additional metadata to log
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(operation, **metadata):
                return func(*args, **kwargs)
        return wrapper
    return decorator 