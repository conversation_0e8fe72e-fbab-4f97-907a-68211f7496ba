# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# DATABASE CONFIGURATION (SUPABASE)
# =============================================================================
# Primary Supabase connection string (required)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Optional: Supabase project details for reference
SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
SUPABASE_ANON_KEY=[YOUR-ANON-KEY]

# Database connection settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_ECHO=false
POSTGRES_SSL_MODE=require

# =============================================================================
# VECTOR DATABASE (QDRANT CLOUD)
# =============================================================================
QDRANT_URL=https://6b928f72-11ec-429b-9078-aa969671fa8a.eu-west-2-0.aws.cloud.qdrant.io
QDRANT_HOST=6b928f72-11ec-429b-9078-aa969671fa8a.eu-west-2-0.aws.cloud.qdrant.io
QDRANT_PORT=443
QDRANT_USE_HTTPS=true
QDRANT_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.RJ_hyM7yA1RnmMA0h3BVm6RiLSKw5rVV853fuHik6_M
QDRANT_COLLECTION=volunteers
QDRANT_VECTOR_SIZE=384
QDRANT_DISTANCE=cosine
QDRANT_TIMEOUT=30

# =============================================================================
# LINKEDIN CREDENTIALS
# =============================================================================
LINKEDIN_EMAIL=[YOUR-LINKEDIN-EMAIL]
LINKEDIN_PASSWORD=[YOUR-LINKEDIN-PASSWORD]

# =============================================================================
# AZURE OPENAI CONFIGURATION
# =============================================================================
AZURE_OPENAI_ENDPOINT=[YOUR-AZURE-OPENAI-ENDPOINT]
AZURE_OPENAI_API_KEY=[YOUR-AZURE-OPENAI-API-KEY]
AZURE_OPENAI_DEPLOYMENT_NAME=[YOUR-DEPLOYMENT-NAME]
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_TEMPERATURE=0.0
AZURE_OPENAI_MAX_TOKENS=4000

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================
# JWT Keys - can be set via environment variables or read from public.key/private.key files in root
JWT_PUBLIC_KEY=
JWT_PRIVATE_KEY=
JWT_ALGORITHM=RS256
JWT_AUDIENCE=
JWT_ISSUER=
RATE_LIMIT_PER_MINUTE=60
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000

# =============================================================================
# API SERVICE CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=false
API_WORKERS=1
MAX_REQUEST_SIZE=10485760
REQUEST_TIMEOUT=30
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# =============================================================================
# AWS CONFIGURATION (OPTIONAL)
# =============================================================================
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=
S3_URL_EXPIRY=3600

# =============================================================================
# WORKER CONFIGURATION
# =============================================================================
EXTRACTION_WORKER_CONCURRENCY=2
EXTRACTION_PREFETCH_COUNT=5
VECTOR_WORKER_CONCURRENCY=2
VECTOR_BATCH_SIZE=8
VECTOR_PREFETCH_COUNT=3

# NLP Model settings
SENTENCE_TRANSFORMER_MODEL=all-MiniLM-L6-v2
SPACY_MODEL=en_core_web_trf

# Processing timeouts (in seconds)
LINKEDIN_TIMEOUT=600
RESUME_TIMEOUT=300

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
PROMETHEUS_PORT=9090
ENABLE_METRICS=true
ENABLE_TRACING=false 