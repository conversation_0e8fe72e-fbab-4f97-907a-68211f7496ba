"""
Application settings and configuration management.

This module provides centralized configuration management using Pydantic settings.
All settings can be configured via environment variables or .env files.
"""

from typing import List, Optional
from pydantic import Field, SecretStr, field_validator
from pydantic_settings import BaseSettings
from urllib.parse import urlparse
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class SupabaseSettings(BaseSettings):
    """Supabase configuration settings."""

    # Supabase connection details
    url: str = Field("", env="SUPABASE_URL")
    anon_key: SecretStr = Field("", env="SUPABASE_ANON_KEY")

    # Legacy DATABASE_URL (kept for reference)
    database_url: Optional[str] = Field(None, env="DATABASE_URL")

    class Config:
        env_prefix = ""


class QdrantSettings(BaseSettings):
    """Qdrant vector database configuration."""
    
    url: str = Field("https://6b928f72-11ec-429b-9078-aa969671fa8a.eu-west-2-0.aws.cloud.qdrant.io", env="QDRANT_URL")
    host: str = Field("6b928f72-11ec-429b-9078-aa969671fa8a.eu-west-2-0.aws.cloud.qdrant.io", env="QDRANT_HOST")
    port: int = Field(443, env="QDRANT_PORT")
    use_https: bool = Field(True, env="QDRANT_USE_HTTPS")
    api_key: Optional[SecretStr] = Field(None, env="QDRANT_API_KEY")
    collection: str = Field("volunteers", env="QDRANT_COLLECTION")
    vector_size: int = Field(384, env="QDRANT_VECTOR_SIZE")
    distance: str = Field("cosine", env="QDRANT_DISTANCE")
    timeout: int = Field(30, env="QDRANT_TIMEOUT")
    
    class Config:
        env_prefix = ""


class LinkedInSettings(BaseSettings):
    """LinkedIn automation configuration."""

    email: str = Field(default_factory=lambda: os.getenv("LINKEDIN_EMAIL", ""))
    password: SecretStr = Field(default_factory=lambda: SecretStr(os.getenv("LINKEDIN_PASSWORD", "")))
    timeout: int = Field(default_factory=lambda: int(os.getenv("LINKEDIN_TIMEOUT", "600")))

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"


class AzureOpenAISettings(BaseSettings):
    """Azure OpenAI configuration."""

    endpoint: str = Field(default_factory=lambda: os.getenv("AZURE_OPENAI_ENDPOINT", ""))
    api_key: SecretStr = Field(default_factory=lambda: SecretStr(os.getenv("AZURE_OPENAI_API_KEY", "")))
    deployment_name: str = Field(default_factory=lambda: os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", ""))
    api_version: str = Field(default_factory=lambda: os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview"))
    temperature: float = Field(default_factory=lambda: float(os.getenv("AZURE_OPENAI_TEMPERATURE", "0.0")))
    max_tokens: int = Field(default_factory=lambda: int(os.getenv("AZURE_OPENAI_MAX_TOKENS", "4000")))

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"


class SecuritySettings(BaseSettings):
    """Security and authentication configuration."""
    
    jwt_public_key: str = Field("", env="JWT_PUBLIC_KEY")
    jwt_private_key: str = Field("", env="JWT_PRIVATE_KEY")
    jwt_algorithm: str = Field("RS256", env="JWT_ALGORITHM")
    jwt_audience: Optional[str] = Field(None, env="JWT_AUDIENCE")
    jwt_issuer: Optional[str] = Field(None, env="JWT_ISSUER")
    rate_limit_per_minute: int = Field(60, env="RATE_LIMIT_PER_MINUTE")
    allowed_origins: str = Field(
        "http://localhost:3000,http://localhost:8000",
        env="ALLOWED_ORIGINS"
    )
    
    def __init__(self, **data):
        """Initialize SecuritySettings with keys from files if not provided."""
        super().__init__(**data)
        
        # Try to read keys from files if not provided via environment variables
        if not self.jwt_public_key:
            self.jwt_public_key = self._read_key_file("public.key")
        if not self.jwt_private_key:
            self.jwt_private_key = self._read_key_file("private.key")
    
    def _read_key_file(self, filename: str) -> str:
        """Read key from file in root directory."""
        try:
            # Get the root directory (where this file is located)
            current_dir = Path(__file__).parent
            root_dir = current_dir.parent
            key_file = root_dir / filename
            
            if key_file.exists():
                return key_file.read_text(encoding="utf-8").strip()
            else:
                return ""
        except Exception as e:
            print(f"Warning: Could not read {filename}: {e}")
            return ""
    
    def get_allowed_origins_list(self) -> List[str]:
        """Get allowed origins as a list."""
        if isinstance(self.allowed_origins, str):
            return [origin.strip() for origin in self.allowed_origins.split(',')]
        return self.allowed_origins
    
    class Config:
        env_prefix = ""


class AWSSettings(BaseSettings):
    """AWS configuration."""
    
    access_key_id: Optional[str] = Field(None, env="AWS_ACCESS_KEY_ID")
    secret_access_key: Optional[SecretStr] = Field(None, env="AWS_SECRET_ACCESS_KEY")
    region: str = Field("us-east-1", env="AWS_REGION")
    s3_bucket: Optional[str] = Field(None, env="AWS_S3_BUCKET")
    s3_url_expiry: int = Field(3600, env="S3_URL_EXPIRY")
    
    class Config:
        env_prefix = ""


class WorkerSettings(BaseSettings):
    """Worker configuration."""
    
    extraction_concurrency: int = Field(2, env="EXTRACTION_WORKER_CONCURRENCY")
    extraction_prefetch_count: int = Field(5, env="EXTRACTION_PREFETCH_COUNT")
    vector_concurrency: int = Field(2, env="VECTOR_WORKER_CONCURRENCY")
    vector_batch_size: int = Field(8, env="VECTOR_BATCH_SIZE")
    vector_prefetch_count: int = Field(3, env="VECTOR_PREFETCH_COUNT")
    
    # NLP Model settings
    sentence_transformer_model: str = Field("all-MiniLM-L6-v2", env="SENTENCE_TRANSFORMER_MODEL")
    spacy_model: str = Field("en_core_web_trf", env="SPACY_MODEL")
    
    # Processing timeouts
    linkedin_timeout: int = Field(600, env="LINKEDIN_TIMEOUT")
    resume_timeout: int = Field(300, env="RESUME_TIMEOUT")
    
    class Config:
        env_prefix = ""


class MonitoringSettings(BaseSettings):
    """Monitoring and observability configuration."""
    
    prometheus_port: int = Field(9090, env="PROMETHEUS_PORT")
    enable_metrics: bool = Field(True, env="ENABLE_METRICS")
    enable_tracing: bool = Field(False, env="ENABLE_TRACING")
    
    class Config:
        env_prefix = ""


class APISettings(BaseSettings):
    """API service configuration."""
    
    host: str = Field("0.0.0.0", env="API_HOST")
    port: int = Field(8000, env="API_PORT")
    reload: bool = Field(False, env="API_RELOAD")
    workers: int = Field(1, env="API_WORKERS")
    max_request_size: int = Field(10485760, env="MAX_REQUEST_SIZE")  # 10MB
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT")
    default_page_size: int = Field(20, env="DEFAULT_PAGE_SIZE")
    max_page_size: int = Field(100, env="MAX_PAGE_SIZE")
    
    class Config:
        env_prefix = ""


class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Service settings
    supabase: SupabaseSettings = Field(default_factory=SupabaseSettings)
    qdrant: QdrantSettings = Field(default_factory=QdrantSettings)
    linkedin: LinkedInSettings = Field(default_factory=LinkedInSettings)
    azure_openai: AzureOpenAISettings = Field(default_factory=AzureOpenAISettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    aws: AWSSettings = Field(default_factory=AWSSettings)
    workers: WorkerSettings = Field(default_factory=WorkerSettings)
    monitoring: MonitoringSettings = Field(default_factory=MonitoringSettings)
    api: APISettings = Field(default_factory=APISettings)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"
        
    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v):
        """Validate environment is one of the allowed values."""
        valid_envs = ["development", "staging", "production", "testing"]
        if v not in valid_envs:
            raise ValueError(f"Environment must be one of {valid_envs}")
        return v
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"
    
    # Convenience properties for Supabase access
    @property
    def supabase_url(self) -> str:
        """Get Supabase URL."""
        return self.supabase.url
    
    @property
    def supabase_anon_key(self) -> str:
        """Get Supabase anon key."""
        return self.supabase.anon_key.get_secret_value()
    
    # Convenience properties for Qdrant access
    @property
    def qdrant_host(self) -> str:
        """Get Qdrant host."""
        return self.qdrant.host
    
    @property
    def qdrant_port(self) -> int:
        """Get Qdrant port."""
        return self.qdrant.port
    
    @property
    def qdrant_url(self) -> str:
        """Get Qdrant URL."""
        return self.qdrant.url
    
    @property
    def qdrant_api_key(self) -> Optional[SecretStr]:
        """Get Qdrant API key."""
        return self.qdrant.api_key
    
    @property
    def qdrant_use_https(self) -> bool:
        """Get Qdrant HTTPS setting."""
        return self.qdrant.use_https
    
    @property
    def qdrant_collection_name(self) -> str:
        """Get Qdrant collection name."""
        return self.qdrant.collection
    
    @property
    def vector_dimension(self) -> int:
        """Get vector dimension."""
        return self.qdrant.vector_size
    
    # Convenience properties for Security access
    @property
    def jwt_public_key(self) -> str:
        """Get JWT public key."""
        return self.security.jwt_public_key
    
    @property
    def jwt_private_key(self) -> str:
        """Get JWT private key."""
        return self.security.jwt_private_key


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings 