"""
Health check endpoints for the skill extractor API.

This module provides endpoints for monitoring system health,
checking component status, and providing readiness/liveness probes.
"""

import asyncio
import time
from typing import List

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from common.models import HealthCheckResponse, HealthStatus, ComponentHealth
from common.settings import settings
from common.logging import get_logger
from services.api.clients.database import database_health_check
from services.api.clients.qdrant import qdrant_health_check
from services.api.clients.browser_use import browser_use_health_check

logger = get_logger(__name__)

router = APIRouter()


async def check_database_health() -> ComponentHealth:
    """Check PostgreSQL database connectivity."""
    
    start_time = time.time()
    
    try:
        health_result = await database_health_check()
        response_time = (time.time() - start_time) * 1000
        
        if health_result.get("status") == "healthy":
            return ComponentHealth(
                name="postgresql",
                status=HealthStatus.HEALTHY,
                message="Database connection successful",
                response_time_ms=response_time
            )
        else:
            return ComponentHealth(
                name="postgresql",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {health_result.get('error', 'Unknown error')}",
                response_time_ms=response_time
            )
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Database health check failed", error=str(e))
        
        return ComponentHealth(
            name="postgresql",
            status=HealthStatus.UNHEALTHY,
            message=f"Database connection failed: {str(e)}",
            response_time_ms=response_time
        )


async def check_qdrant_health() -> ComponentHealth:
    """Check Qdrant vector database connectivity."""
    
    start_time = time.time()
    
    try:
        health_result = await qdrant_health_check()
        response_time = (time.time() - start_time) * 1000
        
        if health_result.get("status") == "healthy":
            return ComponentHealth(
                name="qdrant",
                status=HealthStatus.HEALTHY,
                message="Qdrant connection successful",
                response_time_ms=response_time
            )
        else:
            return ComponentHealth(
                name="qdrant",
                status=HealthStatus.UNHEALTHY,
                message=f"Qdrant connection failed: {health_result.get('error', 'Unknown error')}",
                response_time_ms=response_time
            )
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Qdrant health check failed", error=str(e))
        
        return ComponentHealth(
            name="qdrant",
            status=HealthStatus.UNHEALTHY,
            message=f"Qdrant connection failed: {str(e)}",
            response_time_ms=response_time
        )


async def check_browser_use_health() -> ComponentHealth:
    """Check Browser-Use client connectivity."""
    
    start_time = time.time()
    
    try:
        health_result = await browser_use_health_check()
        response_time = (time.time() - start_time) * 1000
        
        if health_result.get("status") == "healthy":
            return ComponentHealth(
                name="browser_use",
                status=HealthStatus.HEALTHY,
                message="Browser-Use client healthy",
                response_time_ms=response_time
            )
        elif health_result.get("status") == "degraded":
            return ComponentHealth(
                name="browser_use",
                status=HealthStatus.DEGRADED,
                message=f"Browser-Use client degraded: {health_result.get('browser_error', 'Unknown issue')}",
                response_time_ms=response_time
            )
        else:
            return ComponentHealth(
                name="browser_use",
                status=HealthStatus.UNHEALTHY,
                message=f"Browser-Use client unhealthy: {health_result.get('error', 'Unknown error')}",
                response_time_ms=response_time
            )
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Browser-Use health check failed", error=str(e))
        
        return ComponentHealth(
            name="browser_use",
            status=HealthStatus.UNHEALTHY,
            message=f"Browser-Use client failed: {str(e)}",
            response_time_ms=response_time
        )


@router.get("/", response_model=HealthCheckResponse)
@router.get("/check", response_model=HealthCheckResponse)
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Checks the health of all system components including:
    - PostgreSQL database
    - Qdrant vector database
    - Browser-Use client
    
    Returns overall system health status.
    """
    
    logger.debug("Performing comprehensive health check")
    
    # Run all health checks concurrently
    health_checks = await asyncio.gather(
        check_database_health(),
        check_qdrant_health(),
        check_browser_use_health(),
        return_exceptions=True
    )
    
    # Filter out any exceptions and create component health list
    components: List[ComponentHealth] = []
    for check_result in health_checks:
        if isinstance(check_result, ComponentHealth):
            components.append(check_result)
        else:
            # Handle unexpected exceptions
            logger.error("Health check exception", error=str(check_result))
            components.append(ComponentHealth(
                name="unknown_component",
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(check_result)}"
            ))
    
    # Determine overall health status
    overall_status = HealthStatus.HEALTHY
    unhealthy_count = sum(1 for component in components if component.status == HealthStatus.UNHEALTHY)
    degraded_count = sum(1 for component in components if component.status == HealthStatus.DEGRADED)
    
    if unhealthy_count > 0:
        overall_status = HealthStatus.UNHEALTHY
    elif degraded_count > 0:
        overall_status = HealthStatus.DEGRADED
    
    response = HealthCheckResponse(
        status=overall_status,
        components=components
    )
    
    logger.debug(
        "Health check completed",
        overall_status=overall_status.value,
        component_count=len(components),
        unhealthy_count=unhealthy_count,
        degraded_count=degraded_count
    )
    
    return response


@router.get("/live")
async def liveness_probe():
    """
    Kubernetes liveness probe endpoint.
    
    This endpoint checks if the application is alive and responsive.
    It should return 200 if the application can handle requests.
    """
    
    return {
        "status": "alive",
        "timestamp": time.time(),
        "service": "skill-extractor-api"
    }


@router.get("/ready")
async def readiness_probe():
    """
    Kubernetes readiness probe endpoint.
    
    This endpoint checks if the application is ready to receive traffic.
    It performs basic checks on critical dependencies.
    """
    
    try:
        # Perform lightweight checks on critical components
        db_check = await check_database_health()
        qdrant_check = await check_qdrant_health()
        
        # Application is ready if core components are healthy
        if (db_check.status == HealthStatus.HEALTHY and 
            qdrant_check.status == HealthStatus.HEALTHY):
            
            return {
                "status": "ready",
                "timestamp": time.time(),
                "service": "skill-extractor-api"
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "not_ready",
                    "message": "Core dependencies not healthy",
                    "database": db_check.status.value,
                    "qdrant": qdrant_check.status.value
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Readiness probe failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={
                "status": "not_ready",
                "message": "Readiness check failed",
                "error": str(e)
            }
        )


@router.get("/startup")
async def startup_probe():
    """
    Kubernetes startup probe endpoint.
    
    This endpoint checks if the application has started successfully.
    It may take longer than liveness/readiness probes during startup.
    """
    
    try:
        # Perform comprehensive startup checks
        health_response = await health_check()
        
        if health_response.status == HealthStatus.HEALTHY:
            return {
                "status": "started",
                "timestamp": time.time(),
                "service": "skill-extractor-api",
                "components": len(health_response.components)
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "starting",
                    "message": "Application still starting up",
                    "overall_status": health_response.status.value
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Startup probe failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={
                "status": "failed_to_start",
                "message": "Startup check failed",
                "error": str(e)
            }
        ) 