[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "skill-extractor"
version = "0.1.0"
description = "Backend service for extracting and vectorizing skills from LinkedIn profiles and résumé PDFs"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Skill Extractor Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.12"
dependencies = [
    # Web Framework
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # Database & ORM
    "sqlmodel>=0.0.14",
    "alembic>=1.12.0",
    "psycopg2-binary>=2.9.7",
    
    # Pydantic & Settings
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Vector Database
    "qdrant-client>=1.7.0",
    
    # Message Queue

    
    # NLP & ML
    "spacy>=3.7.0",
    "sentence-transformers>=2.2.2",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "numpy>=1.24.0",
    
    # Browser Automation & Web Scraping
    "browser-use>=1.0.0",
    "langchain-openai>=0.1.0",
    "aiohttp>=3.9.0",
    "requests>=2.31.0",
    
    # PDF Processing
    "pdfplumber>=0.10.0",
    "pytesseract>=0.3.10",
    "Pillow>=10.0.0",
    
    # AWS & Cloud
    "boto3>=1.34.0",
    "botocore>=1.34.0",
    
    # Security & Auth
    "PyJWT[crypto]>=2.8.0",
    "cryptography>=41.0.0",
    "passlib[bcrypt]>=1.7.4",
    
    # Utilities
    "tenacity>=8.2.0",
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.6",
    "httpx>=0.25.0",
    
    # Monitoring & Observability
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
    
    # Data Processing
    "pandas>=2.1.0",
    "python-dateutil>=2.8.2",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.0",  # For FastAPI testing
    "testcontainers>=3.7.0",
    
    # Code Quality
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "flake8>=6.1.0",
    "pre-commit>=3.5.0",
    
    # Type Stubs
    "types-requests>=2.31.0",
    "types-python-dateutil>=2.8.19",
    "types-Pillow>=10.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/skill-extractor"
Repository = "https://github.com/your-org/skill-extractor.git"
Issues = "https://github.com/your-org/skill-extractor/issues"

[project.scripts]
skill-extractor-api = "services.api.main:main"
skill-extractor-worker-extraction = "services.worker_extraction.main:main"
skill-extractor-worker-vector = "services.worker_vector.main:main"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["common", "services"]
known_third_party = ["fastapi", "pydantic", "sqlmodel", "qdrant_client", "spacy"]

# mypy configuration
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "browser_use.*",
    "langchain_openai.*",
    "qdrant_client.*",
    "pdfplumber.*",
    "pytesseract.*",
    "spacy.*",
    "sentence_transformers.*",
    "testcontainers.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "slow: marks tests as slow running",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["common", "services"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
] 