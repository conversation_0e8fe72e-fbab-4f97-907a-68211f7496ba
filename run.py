#!/usr/bin/env python3
"""
Main entry point for the WeDoGood Skill Extractor application.

This script provides a convenient way to start the WeDoGood Volunteer Data Extraction
API service. The application extracts skills from LinkedIn profiles and enables
volunteer matching through vector similarity search.

Usage:
    python run.py                    # Start the API service
    python run.py --help             # Show help information
    python run.py --version          # Show version information
    python run.py --check-env        # Check environment configuration
"""

import argparse
import sys
import os
from pathlib import Path
from typing import Optional

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

import uvicorn
from common.settings import settings
from common.logging import configure_logging, get_logger


def setup_logging(service_name: str = "run") -> None:
    """Configure logging for the application."""
    configure_logging(
        service_name=service_name,
        log_level=settings.log_level,
        json_logs=settings.environment == "production"
    )


def start_api_service() -> None:
    """Start the WeDoGood Skill Extractor API service."""
    logger = get_logger(__name__)

    logger.info("Starting WeDoGood Skill Extractor API service")
    app_module = "services.api.main:app"
    service_name = "api"

    setup_logging(service_name)
    
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"API Host: {settings.api.host}")
    logger.info(f"API Port: {settings.api.port}")
    
    # Start the server
    uvicorn.run(
        app_module,
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=False,  # We handle logging in middleware
        workers=1 if settings.debug else settings.api.workers,
    )


def check_environment() -> bool:
    """Check if the environment is properly configured."""
    logger = get_logger(__name__)
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        logger.warning("No .env file found. Using default settings.")
        logger.info("Copy env.template to .env and configure your settings.")
        return False
    
    # Check critical settings
    missing_settings = []
    
    if not settings.supabase.database_url and not settings.supabase.url:
        missing_settings.append("DATABASE_URL or SUPABASE_URL")
    
    if not settings.qdrant.url:
        missing_settings.append("QDRANT_URL")
    
    if missing_settings:
        logger.warning(f"Missing critical environment variables: {', '.join(missing_settings)}")
        logger.info("Please check your .env file configuration.")
        return False
    
    return True


def show_version() -> None:
    """Display version information."""
    print("Skill Extractor Backend")
    print("Version: 1.0.0")
    print("Python:", sys.version)
    print("Environment:", settings.environment)
    print("Debug:", settings.debug)


def show_help() -> None:
    """Display help information."""
    help_text = """
WeDoGood Skill Extractor Backend - Main Entry Point

Usage:
    python run.py [OPTIONS]

Options:
    --version, -v          Show version information
    --help, -h             Show this help message
    --check-env            Check environment configuration
    --host HOST            Override API host (default: 0.0.0.0)
    --port PORT            Override API port (default: 8000)
    --reload               Enable auto-reload (development mode)
    --workers N            Number of worker processes (default: 1)

Examples:
    python run.py                    # Start the API service
    python run.py --check-env        # Check environment configuration
    python run.py --host 127.0.0.1 --port 8080  # Custom host and port

Environment:
    The application uses environment variables from .env file.
    Copy env.template to .env and configure your settings.

Features:
    - LinkedIn Profile Ingestion: Extract skills from LinkedIn profiles
    - Health Checks: Available at /health/live, /health/ready, /health/check
    - API Documentation: Available at /docs
    - Metrics: Available at /metrics
    - Vector Search: Similarity matching for volunteers

For more information, see README.md and API_TESTING_REPORT.md
"""
    print(help_text)


def main() -> None:
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="WeDoGood Skill Extractor Backend - Main Entry Point",
        add_help=False
    )
    

    
    parser.add_argument(
        "--version", "-v",
        action="store_true",
        help="Show version information"
    )
    
    parser.add_argument(
        "--help", "-h",
        action="store_true",
        help="Show help information"
    )
    
    parser.add_argument(
        "--check-env",
        action="store_true",
        help="Check environment configuration"
    )
    
    parser.add_argument(
        "--host",
        type=str,
        help="Override API host"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        help="Override API port"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload (development mode)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        help="Number of worker processes"
    )
    
    args = parser.parse_args()
    
    # Handle special commands
    if args.help:
        show_help()
        return
    
    if args.version:
        show_version()
        return
    
    if args.check_env:
        setup_logging("env-check")
        if check_environment():
            print("✅ Environment configuration is valid")
        else:
            print("❌ Environment configuration has issues")
            sys.exit(1)
        return
    
    # Override settings if provided
    if args.host:
        settings.api.host = args.host
    
    if args.port:
        settings.api.port = args.port
    
    if args.reload:
        settings.debug = True
    
    if args.workers:
        settings.api.workers = args.workers
    
    # Check environment before starting
    if not check_environment():
        logger = get_logger(__name__)
        logger.warning("Environment check failed, but continuing...")
    
    # Start the appropriate service
    try:
        start_api_service()
    except KeyboardInterrupt:
        logger = get_logger(__name__)
        logger.info("Application stopped by user")
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()