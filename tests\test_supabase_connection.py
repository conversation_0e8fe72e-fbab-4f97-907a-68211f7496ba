#!/usr/bin/env python3
"""
Supabase Connection Test Script

This script tests the connection to your Supabase PostgreSQL database
and verifies that the basic operations work correctly.

Usage:
    python scripts/test_supabase_connection.py

Make sure to set your DATABASE_URL environment variable first:
    export DATABASE_URL="postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import Optional

try:
    import asyncpg
except ImportError:
    print("❌ Error: asyncpg not installed. Run: pip install asyncpg")
    sys.exit(1)

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️  Warning: python-dotenv not installed. Make sure DATABASE_URL is set as environment variable.")


async def test_supabase_connection(database_url: Optional[str] = None) -> bool:
    """
    Test Supabase PostgreSQL connection and basic operations.
    
    Args:
        database_url: PostgreSQL connection string. If None, reads from environment.
        
    Returns:
        True if all tests pass, False otherwise.
    """
    
    # Get database URL
    if not database_url:
        database_url = os.getenv("DATABASE_URL")
    
    if not database_url:
        print("❌ Error: DATABASE_URL not found in environment variables")
        print("   Please set DATABASE_URL or create a .env file with your Supabase connection string")
        print("   Example: DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres")
        return False
    
    print("🔍 Testing Supabase PostgreSQL connection...")
    print(f"   Host: {database_url.split('@')[1].split(':')[0] if '@' in database_url else 'unknown'}")
    
    try:
        # Test basic connection
        print("\n1️⃣ Testing basic connection...")
        conn = await asyncpg.connect(database_url)
        print("   ✅ Connected successfully!")
        
        # Test PostgreSQL version
        print("\n2️⃣ Testing PostgreSQL version...")
        version = await conn.fetchval("SELECT version()")
        print(f"   ✅ PostgreSQL version: {version.split()[1]}")
        
        # Test SSL connection
        print("\n3️⃣ Testing SSL connection...")
        ssl_status = await conn.fetchval("SHOW ssl")
        print(f"   ✅ SSL enabled: {ssl_status}")
        
        # Test current user and database
        print("\n4️⃣ Testing database access...")
        current_user = await conn.fetchval("SELECT current_user")
        current_database = await conn.fetchval("SELECT current_database()")
        print(f"   ✅ Connected as user: {current_user}")
        print(f"   ✅ Connected to database: {current_database}")
        
        # Test table creation permissions
        print("\n5️⃣ Testing table operations...")
        test_table_name = f"test_connection_{int(datetime.now().timestamp())}"
        
        # Create test table
        await conn.execute(f"""
            CREATE TABLE {test_table_name} (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        print(f"   ✅ Created test table: {test_table_name}")
        
        # Insert test data
        await conn.execute(f"""
            INSERT INTO {test_table_name} (name) VALUES ($1)
        """, "test_record")
        print("   ✅ Inserted test record")
        
        # Query test data
        result = await conn.fetchrow(f"""
            SELECT id, name, created_at FROM {test_table_name} WHERE name = $1
        """, "test_record")
        print(f"   ✅ Retrieved test record: ID={result['id']}, Name={result['name']}")
        
        # Test JSONB operations (important for profile_data)
        print("\n6️⃣ Testing JSONB operations...")
        await conn.execute(f"""
            ALTER TABLE {test_table_name} ADD COLUMN data JSONB DEFAULT '{{}}'
        """)
        
        await conn.execute(f"""
            UPDATE {test_table_name} 
            SET data = $1 
            WHERE name = $2
        """, '{"skills": ["Python", "FastAPI"], "experience": "5 years"}', "test_record")
        
        jsonb_result = await conn.fetchval(f"""
            SELECT data->'skills' FROM {test_table_name} WHERE name = $1
        """, "test_record")
        print(f"   ✅ JSONB operations working: {jsonb_result}")
        
        # Test array operations (important for source_types)
        print("\n7️⃣ Testing array operations...")
        await conn.execute(f"""
            ALTER TABLE {test_table_name} ADD COLUMN tags TEXT[] DEFAULT '{{}}'
        """)
        
        await conn.execute(f"""
            UPDATE {test_table_name} 
            SET tags = $1 
            WHERE name = $2
        """, ["linkedin", "resume"], "test_record")
        
        array_result = await conn.fetchval(f"""
            SELECT tags FROM {test_table_name} WHERE name = $1
        """, "test_record")
        print(f"   ✅ Array operations working: {array_result}")
        
        # Test UUID operations (important for volunteer IDs)
        print("\n8️⃣ Testing UUID operations...")
        uuid_result = await conn.fetchval("SELECT gen_random_uuid()")
        print(f"   ✅ UUID generation working: {str(uuid_result)[:8]}...")
        
        # Clean up test table
        await conn.execute(f"DROP TABLE {test_table_name}")
        print(f"   ✅ Cleaned up test table: {test_table_name}")
        
        # Test connection pooling simulation
        print("\n9️⃣ Testing multiple connections...")
        connections = []
        try:
            for i in range(3):
                test_conn = await asyncpg.connect(database_url)
                connections.append(test_conn)
            print(f"   ✅ Multiple connections working: {len(connections)} connections created")
        finally:
            for test_conn in connections:
                await test_conn.close()
        
        # Close main connection
        await conn.close()
        print("\n🎉 All tests passed! Your Supabase database is ready for the Skill Extractor project.")
        return True
        
    except asyncpg.InvalidCatalogNameError:
        print("❌ Error: Database does not exist")
        print("   Check your DATABASE_URL and ensure the database name is correct")
        return False
        
    except asyncpg.InvalidPasswordError:
        print("❌ Error: Authentication failed")
        print("   Check your password in the DATABASE_URL")
        print("   You can reset your password in the Supabase dashboard")
        return False
        
    except asyncpg.CannotConnectNowError:
        print("❌ Error: Cannot connect to database")
        print("   The database might be starting up. Wait a few minutes and try again.")
        return False
        
    except asyncpg.ConnectionDoesNotExistError:
        print("❌ Error: Connection does not exist")
        print("   Check your host and port in the DATABASE_URL")
        return False
        
    except Exception as e:
        print(f"❌ Error: {type(e).__name__}: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("   1. Check your DATABASE_URL format")
        print("   2. Ensure your Supabase project is fully initialized")
        print("   3. Verify your network connection")
        print("   4. Check Supabase service status")
        return False


def print_usage():
    """Print usage instructions."""
    print("""
Supabase Connection Test Script

This script tests your Supabase PostgreSQL connection and verifies basic operations.

Setup:
1. Create a Supabase project at https://supabase.com
2. Get your connection string from Settings → Database
3. Set the DATABASE_URL environment variable or create a .env file

Usage:
    python scripts/test_supabase_connection.py

Environment Variables:
    DATABASE_URL - PostgreSQL connection string (required)

Example .env file:
    DATABASE_URL=postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres

For more help, see docs/supabase-setup.md
""")


async def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        print_usage()
        return
    
    print("🚀 Supabase Connection Test")
    print("=" * 50)
    
    success = await test_supabase_connection()
    
    if success:
        print("\n✅ SUCCESS: Your Supabase database is configured correctly!")
        print("   You can now start the Skill Extractor API service.")
        sys.exit(0)
    else:
        print("\n❌ FAILED: There were issues with your Supabase configuration.")
        print("   Please check the error messages above and refer to docs/supabase-setup.md")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1) 