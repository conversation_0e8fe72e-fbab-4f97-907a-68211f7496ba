#!/usr/bin/env python3
"""
Debug script to test FastAPI LinkedIn extraction directly
"""

import asyncio
import json
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
import sys
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.api.clients.browser_use import BrowserUseClient
from common.logging import configure_logging, get_logger

async def test_browser_use_client():
    """Test the BrowserUseClient directly to see what's failing."""
    
    # Configure logging
    configure_logging(
        service_name="debug",
        log_level="DEBUG",
        json_logs=False
    )
    
    logger = get_logger(__name__)
    
    print("🔍 Testing BrowserUseClient directly...")
    
    try:
        # Initialize the client
        print("📝 Initializing BrowserUseClient...")
        client = BrowserUseClient()
        
        print("✅ BrowserUseClient initialized successfully")
        
        # Test health check
        print("🏥 Testing health check...")
        health = await client.health_check()
        print(f"Health check result: {json.dumps(health, indent=2)}")
        
        # Test extraction
        profile_url = "https://www.linkedin.com/in/yashkhivasara/"
        print(f"🎯 Testing extraction for: {profile_url}")
        
        result = await client.extract_linkedin_profile(profile_url)
        
        if result:
            print("✅ Extraction successful!")
            print(f"Profile name: {result.get('full_name', 'N/A')}")
            print(f"Headline: {result.get('headline', 'N/A')}")
            print(f"Work experience count: {len(result.get('work_experience', []))}")
        else:
            print("❌ Extraction failed - no result returned")
            
    except Exception as e:
        logger.error(f"Error during testing: {e}", exc_info=True)
        print(f"❌ Error: {e}")

async def test_settings():
    """Test settings configuration."""
    from common.settings import settings
    
    print("🔧 Testing settings configuration...")
    print(f"LinkedIn email configured: {bool(settings.linkedin.email)}")
    print(f"LinkedIn password configured: {bool(settings.linkedin.password)}")
    print(f"Azure OpenAI endpoint: {settings.azure_openai.endpoint[:50] if settings.azure_openai.endpoint else 'None'}...")
    print(f"Azure OpenAI deployment: {settings.azure_openai.deployment_name}")
    print(f"Azure OpenAI API version: {settings.azure_openai.api_version}")
    
    # Test direct environment variable access
    print("\n🌍 Testing direct environment variable access...")
    print(f"LINKEDIN_EMAIL: {os.getenv('LINKEDIN_EMAIL', 'Not set')}")
    print(f"LINKEDIN_PASSWORD: {'Set' if os.getenv('LINKEDIN_PASSWORD') else 'Not set'}")
    print(f"AZURE_OPENAI_ENDPOINT: {os.getenv('AZURE_OPENAI_ENDPOINT', 'Not set')}")
    print(f"AZURE_OPENAI_DEPLOYMENT_NAME: {os.getenv('AZURE_OPENAI_DEPLOYMENT_NAME', 'Not set')}")

async def main():
    """Main test function."""
    print("🚀 FastAPI LinkedIn Extraction Debug")
    print("=" * 50)
    
    await test_settings()
    print("\n" + "=" * 50)
    await test_browser_use_client()

if __name__ == "__main__":
    asyncio.run(main())
