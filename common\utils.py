"""
Utility functions for the skill extractor backend.

This module provides common utility functions including UUID generation
from emails, email hashing for privacy, and other helper functions.
"""

import hashlib
import uuid
from typing import Optional, Union


# UUID namespace for volunteer IDs (deterministic generation)
VOLUNTEER_NAMESPACE = uuid.UUID("6ba7b810-9dad-11d1-80b4-00c04fd430c8")

# Salt for email hashing (should be configured via environment in production)
EMAIL_HASH_SALT = "skill_extractor_email_salt_2024"


def volunteer_id_from_email(email: str) -> uuid.UUID:
    """
    Generate a deterministic UUID v5 for a volunteer based on their email.
    
    This ensures that the same email always generates the same volunteer ID,
    allowing for deduplication and consistent identification.
    
    Args:
        email: The volunteer's email address
        
    Returns:
        UUID v5 generated from the email
        
    Example:
        >>> volunteer_id = volunteer_id_from_email("<EMAIL>")
        >>> print(volunteer_id)
        550e8400-e29b-41d4-a716-************
    """
    # Normalize email to lowercase for consistency
    normalized_email = email.lower().strip()
    
    # Generate UUID v5 using the volunteer namespace and normalized email
    return uuid.uuid5(VOLUNTEER_NAMESPACE, normalized_email)


def hash_email(email: str, salt: Optional[str] = None) -> str:
    """
    Hash an email address using SHA-256 with salt for privacy protection.
    
    This is useful for storing email references without storing the actual
    email address, maintaining privacy while allowing for lookups.
    
    Args:
        email: The email address to hash
        salt: Optional salt to use (defaults to global salt)
        
    Returns:
        Hexadecimal SHA-256 hash of the salted email
        
    Example:
        >>> hashed = hash_email("<EMAIL>")
        >>> print(hashed)
        a1b2c3d4e5f6...
    """
    if salt is None:
        salt = EMAIL_HASH_SALT
    
    # Normalize email to lowercase for consistency
    normalized_email = email.lower().strip()
    
    # Create salted email string
    salted_email = f"{salt}{normalized_email}"
    
    # Generate SHA-256 hash
    hash_object = hashlib.sha256(salted_email.encode('utf-8'))
    return hash_object.hexdigest()


def generate_uuid() -> uuid.UUID:
    """
    Generate a new random UUID v4.
    
    Returns:
        New random UUID
    """
    return uuid.uuid4()


def is_valid_uuid(uuid_string: Union[str, uuid.UUID]) -> bool:
    """
    Check if a string is a valid UUID.
    
    Args:
        uuid_string: String or UUID to validate
        
    Returns:
        True if valid UUID, False otherwise
        
    Example:
        >>> is_valid_uuid("550e8400-e29b-41d4-a716-************")
        True
        >>> is_valid_uuid("invalid-uuid")
        False
    """
    if isinstance(uuid_string, uuid.UUID):
        return True
    
    try:
        uuid.UUID(str(uuid_string))
        return True
    except (ValueError, TypeError):
        return False


def normalize_text(text: str) -> str:
    """
    Normalize text for consistent processing.
    
    Args:
        text: Text to normalize
        
    Returns:
        Normalized text with consistent whitespace and encoding
    """
    if not text:
        return ""
    
    # Strip whitespace and normalize to single spaces
    normalized = " ".join(text.strip().split())
    
    return normalized


def extract_domain_from_email(email: str) -> str:
    """
    Extract the domain part from an email address.
    
    Args:
        email: Email address
        
    Returns:
        Domain part of the email
        
    Example:
        >>> extract_domain_from_email("<EMAIL>")
        "example.com"
    """
    normalized_email = email.lower().strip()
    if "@" in normalized_email:
        return normalized_email.split("@")[1]
    return ""


def safe_filename(filename: str, max_length: int = 255) -> str:
    """
    Create a safe filename by removing/replacing problematic characters.
    
    Args:
        filename: Original filename
        max_length: Maximum length for the filename
        
    Returns:
        Safe filename suitable for filesystem storage
    """
    if not filename:
        return "unnamed_file"
    
    # Replace problematic characters
    safe_chars = []
    for char in filename:
        if char.isalnum() or char in ".-_":
            safe_chars.append(char)
        else:
            safe_chars.append("_")
    
    safe_name = "".join(safe_chars)
    
    # Ensure it doesn't start with a dot
    if safe_name.startswith("."):
        safe_name = "file" + safe_name
    
    # Truncate if too long
    if len(safe_name) > max_length:
        name_part, ext_part = safe_name.rsplit(".", 1) if "." in safe_name else (safe_name, "")
        max_name_length = max_length - len(ext_part) - 1 if ext_part else max_length
        safe_name = name_part[:max_name_length] + ("." + ext_part if ext_part else "")
    
    return safe_name


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate text to a maximum length with optional suffix.
    
    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    if len(suffix) >= max_length:
        return text[:max_length]
    
    return text[:max_length - len(suffix)] + suffix


def chunks(lst: list, chunk_size: int):
    """
    Yield successive chunks from a list.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Yields:
        Chunks of the list
        
    Example:
        >>> list(chunks([1, 2, 3, 4, 5], 2))
        [[1, 2], [3, 4], [5]]
    """
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]


def merge_dicts(*dicts, deep: bool = False):
    """
    Merge multiple dictionaries.
    
    Args:
        *dicts: Dictionaries to merge
        deep: Whether to perform deep merge for nested dictionaries
        
    Returns:
        Merged dictionary
    """
    result = {}
    
    for d in dicts:
        if not isinstance(d, dict):
            continue
            
        for key, value in d.items():
            if deep and key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_dicts(result[key], value, deep=True)
            else:
                result[key] = value
    
    return result 